<template>
	<view class="custody-attendance-container">
		<!-- 自定义导航栏 -->
		<view class="custom-navbar">
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<text class="back-icon">←</text>
				</view>
				<view class="navbar-title">
					<text class="title-text">托管考勤管理</text>
				</view>
				<view class="navbar-right">
					<view class="date-selector" @click="openDatePicker">
						<text class="date-text">{{ currentDate }}</text>
						<text class="calendar-icon">📅</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 统计卡片 -->
		<view class="statistics-section">
			<view class="statistics-grid">
				<view class="stat-card total-card">
					<view class="stat-icon">👥</view>
					<view class="stat-content">
						<text class="stat-number">{{ statistics.totalStudents }}</text>
						<text class="stat-label">总人数</text>
					</view>
				</view>
				<view class="stat-card present-card">
					<view class="stat-icon">✅</view>
					<view class="stat-content">
						<text class="stat-number">{{ statistics.presentCount }}</text>
						<text class="stat-label">出勤人数</text>
					</view>
				</view>
				<view class="stat-card absent-card">
					<view class="stat-icon">❌</view>
					<view class="stat-content">
						<text class="stat-number">{{ statistics.absentCount }}</text>
						<text class="stat-label">缺勤人数</text>
					</view>
				</view>
				<view class="stat-card rate-card">
					<view class="stat-icon">📊</view>
					<view class="stat-content">
						<text class="stat-number">{{ statistics.attendanceRate }}%</text>
						<text class="stat-label">出勤率</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 筛选条件 -->
		<view class="filter-section">
			<view class="filter-row">
				<view class="filter-item">
					<text class="filter-label">课程筛选</text>
					<picker
						:value="selectedCourseIndex"
						:range="courseOptions"
						range-key="name"
						@change="onCourseChange"
					>
						<view class="picker-display">
							<text class="picker-text">{{ selectedCourse || '全部课程' }}</text>
							<text class="picker-arrow">▼</text>
						</view>
					</picker>
				</view>
				<view class="filter-item">
					<text class="filter-label">状态筛选</text>
					<picker
						:value="selectedStatusIndex"
						:range="statusOptions"
						range-key="name"
						@change="onStatusChange"
					>
						<view class="picker-display">
							<text class="picker-text">{{ selectedStatus || '全部状态' }}</text>
							<text class="picker-arrow">▼</text>
						</view>
					</picker>
				</view>
			</view>
		</view>

		<!-- 操作按钮区域 -->
		<view class="action-section">
			<view class="action-buttons">
				<view class="action-btn primary-btn" @click="handleBatchCheckin">
					<text class="btn-icon">✅</text>
					<text class="btn-text">批量签到</text>
				</view>
				<view class="action-btn success-btn" @click="handleBatchConfirm">
					<text class="btn-icon">✔️</text>
					<text class="btn-text">批量确认</text>
				</view>
				<view class="action-btn warning-btn" @click="handleExport">
					<text class="btn-icon">📊</text>
					<text class="btn-text">导出数据</text>
				</view>
			</view>
		</view>

		<!-- 搜索栏 -->
		<view class="search-section">
			<view class="search-box">
				<text class="search-icon">🔍</text>
				<input 
					class="search-input" 
					type="text" 
					placeholder="请输入学生姓名"
					v-model="searchKeyword"
					@input="handleSearch"
				/>
				<view v-if="searchKeyword" class="clear-icon" @click="clearSearch">
					<text>×</text>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<text class="loading-text">加载中...</text>
		</view>

		<!-- 空状态 -->
		<view v-else-if="attendanceData.length === 0" class="empty-state">
			<view class="empty-icon">📋</view>
			<text class="empty-text">暂无托管考勤数据</text>
			<view class="empty-action" @click="loadAttendanceData">
				<text class="action-text">点击重新加载</text>
			</view>
		</view>

		<!-- 托管考勤列表 -->
		<view v-else class="attendance-list">
			<view v-for="courseGroup in filteredAttendanceData" :key="courseGroup.courseValue" class="course-group">
				<!-- 课程头部 -->
				<view class="course-header" @click="toggleCourseCollapse(courseGroup.courseValue)">
					<view class="course-info">
						<text class="course-name">{{ courseGroup.courseName }}</text>
						<text class="course-count">{{ courseGroup.students.length }}人</text>
					</view>
					<view class="collapse-icon" :class="{ collapsed: isCourseCollapsed(courseGroup.courseValue) }">
						<text>{{ isCourseCollapsed(courseGroup.courseValue) ? '▶' : '▼' }}</text>
					</view>
				</view>

				<!-- 学生列表 -->
				<view v-if="!isCourseCollapsed(courseGroup.courseValue)" class="students-list">
					<view v-for="student in courseGroup.students" :key="student.id"
						class="student-card"
						:class="{ 'selected': selectedStudents.includes(student.id) }"
						@click="toggleStudentSelection(student)">

						<!-- 选择框 -->
						<view class="selection-box" :class="{ 'selected': selectedStudents.includes(student.id) }">
							<text class="selection-icon">{{ selectedStudents.includes(student.id) ? '✓' : '' }}</text>
						</view>

						<!-- 学生信息 -->
						<view class="student-info">
							<view class="student-avatar">
								<text class="avatar-text">{{ student.name.charAt(0) }}</text>
							</view>
							<view class="student-details">
								<text class="student-name">{{ student.name }}</text>
								<text class="student-course">{{ courseGroup.courseName }}</text>
							</view>
						</view>

						<!-- 考勤状态 -->
						<view class="attendance-status">
							<view class="status-badge" :class="getStatusClass(student.status)">
								<text class="status-icon">{{ getStatusIcon(student.status) }}</text>
								<text class="status-text">{{ getStatusText(student.status) }}</text>
							</view>
							<view class="confirm-status" :class="{ 'confirmed': student.isConfirmed }">
								<text class="confirm-text">{{ student.isConfirmed ? '已确认' : '待确认' }}</text>
							</view>
						</view>

						<!-- 时间信息 -->
						<view class="time-info">
							<view class="time-item" v-if="student.checkInTime">
								<text class="time-label">签到：</text>
								<text class="time-value">{{ student.checkInTime }}</text>
							</view>
							<view class="time-item" v-if="student.checkOutTime">
								<text class="time-label">签退：</text>
								<text class="time-value">{{ student.checkOutTime }}</text>
							</view>
							<view class="time-item" v-if="!student.checkInTime && student.status === 'absent'">
								<text class="time-label">状态：</text>
								<text class="time-value absent">未到课</text>
							</view>
						</view>

						<!-- 操作按钮 -->
						<view class="action-buttons">
							<view v-if="!student.isConfirmed" class="action-btn confirm-btn" @click.stop="confirmSingleAttendance(student)">
								<text>确认</text>
							</view>
							<view class="action-btn detail-btn" @click.stop="showAttendanceDetail(student)">
								<text>详情</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast } from '@/utils/utils.js'
// import { getCustodyAttendanceOverview, custodyCheckin } from '@/api/custodyAttendance.js'

export default {
	name: 'CustodyAttendance',
	data() {
		return {
			// 基础数据
			loading: false,
			currentDate: '',
			currentDateValue: '',
			attendanceData: [],
			originalAttendanceData: [],
			courseList: [],

			// 统计数据
			statistics: {
				totalStudents: 0,
				presentCount: 0,
				absentCount: 0,
				attendanceRate: 0
			},

			// 筛选相关
			selectedCourse: '',
			selectedCourseIndex: 0,
			selectedStatus: '',
			selectedStatusIndex: 0,
			courseOptions: [
				{ name: '全部课程', value: '' }
			],
			statusOptions: [
				{ name: '全部状态', value: '' },
				{ name: '出勤', value: 'present' },
				{ name: '缺勤', value: 'absent' },
				{ name: '请假', value: 'leave' }
			],
			collapsedCourses: [],

			// 搜索相关
			searchKeyword: '',

			// 选择相关
			selectedStudents: [],

			// UI状态
			currentStudent: null
		}
	},
	
	computed: {
		filteredAttendanceData() {
			let data = this.attendanceData

			// 课程筛选
			if (this.selectedCourse && this.selectedCourse !== '') {
				data = data.filter(courseGroup => courseGroup.courseValue === this.selectedCourse)
			}

			// 状态筛选
			if (this.selectedStatus && this.selectedStatus !== '') {
				data = data.map(courseGroup => {
					const filteredStudents = courseGroup.students.filter(student =>
						student.status === this.selectedStatus
					)

					if (filteredStudents.length > 0) {
						return {
							...courseGroup,
							students: filteredStudents
						}
					}
					return null
				}).filter(courseGroup => courseGroup !== null)
			}

			// 搜索筛选
			if (this.searchKeyword.trim()) {
				const keyword = this.searchKeyword.trim().toLowerCase()
				data = data.map(courseGroup => {
					const filteredStudents = courseGroup.students.filter(student =>
						student.name.toLowerCase().includes(keyword)
					)

					if (filteredStudents.length > 0) {
						return {
							...courseGroup,
							students: filteredStudents
						}
					}
					return null
				}).filter(courseGroup => courseGroup !== null)
			}

			return data
		}
	},
	
	onLoad() {
		this.initializePage()
	},
	
	onShow() {
		// 页面显示时刷新数据
		if (this.attendanceData.length > 0) {
			this.loadAttendanceData(false)
		}
		this.updateCourseCollapseState()
	},
	
	methods: {
		// 初始化页面
		initializePage() {
			this.setCurrentDate()
			this.loadAttendanceData()
		},

		// 设置当前日期
		setCurrentDate() {
			const today = new Date()
			this.currentDate = today.toLocaleDateString('zh-CN', {
				month: '2-digit',
				day: '2-digit'
			})
			this.currentDateValue = today.toISOString().split('T')[0]
		},

		// 加载托管考勤数据
		async loadAttendanceData(showLoading = true) {
			if (showLoading) {
				this.loading = true
			}

			try {
				// TODO: 调用托管考勤API
				// const response = await getCustodyAttendanceOverview(params)
				
				// 模拟数据
				const mockData = [
					{
						courseId: 1,
						courseName: '数学辅导',
						students: [
							{
								id: 1,
								name: '张小明',
								checkInTime: '14:30',
								checkOutTime: '',
								status: 'present',
								isCheckedIn: true
							},
							{
								id: 2,
								name: '李小红',
								checkInTime: '',
								checkOutTime: '',
								status: 'absent',
								isCheckedIn: false
							}
						]
					}
				]
				
				this.originalAttendanceData = this.formatAttendanceData(mockData)
				this.filterAttendanceData()
				this.calculateStats()
				
			} catch (error) {
				console.error('加载托管考勤数据失败:', error)
				toast('加载数据失败，请重试')
			} finally {
				this.loading = false
			}
		},

		// 格式化考勤数据
		formatAttendanceData(data) {
			if (!data || !Array.isArray(data)) {
				return []
			}
			
			const courseMap = new Map()

			data.forEach(item => {
				const courseId = item.courseId
				const courseName = item.courseName || '未分配课程'

				if (!courseMap.has(courseId)) {
					courseMap.set(courseId, {
						courseName: courseName,
						courseValue: String(courseId),
						students: []
					})
					
					// 默认所有课程都折叠
					const courseValue = String(courseId)
					if (!this.collapsedCourses.includes(courseValue)) {
						this.collapsedCourses.push(courseValue)
					}
				}

				// 处理学生数据
				item.students.forEach(student => {
					const formattedStudent = {
						id: student.id,
						name: student.name,
						checkInTime: student.checkInTime || '',
						checkOutTime: student.checkOutTime || '',
						status: student.status || 'absent',
						isCheckedIn: !!student.checkInTime,
						attendanceId: student.attendanceId || null
					}

					courseMap.get(courseId).students.push(formattedStudent)
				})
			})

			return Array.from(courseMap.values())
		},

		// 过滤考勤数据
		filterAttendanceData() {
			this.attendanceData = this.originalAttendanceData
		},

		// 计算统计数据
		calculateStats() {
			let total = 0
			let present = 0
			let absent = 0

			// 始终基于原始数据计算统计，不受筛选影响
			this.originalAttendanceData.forEach(courseGroup => {
				courseGroup.students.forEach(student => {
					total++

					if (student.status === 'present') {
						present++
					} else if (student.status === 'absent') {
						absent++
					}
				})
			})

			this.statistics.totalStudents = total
			this.statistics.presentCount = present
			this.statistics.absentCount = absent
			this.statistics.attendanceRate = total > 0 ? Math.round((present / total) * 100) : 0
		},

		// 返回上一页
		goBack() {
			uni.navigateBack()
		},

		// 打开日期选择器
		openDatePicker() {
			toast('日期选择功能开发中...')
		},

		// 设置筛选条件
		setFilter(filterType) {
			this.currentFilter = filterType
			this.updateCourseCollapseState()
		},

		// 搜索处理
		handleSearch() {
			if (this.searchTimer) {
				clearTimeout(this.searchTimer)
			}

			this.searchTimer = setTimeout(() => {
				this.updateCourseCollapseState()
				this.searchTimer = null
			}, 300)
		},

		// 清除搜索
		clearSearch() {
			this.searchKeyword = ''
			this.updateCourseCollapseState()
		},

		// 更新课程折叠状态
		updateCourseCollapseState() {
			if (this.searchKeyword.trim() || this.currentFilter !== 'all') {
				// 有搜索关键词或筛选条件时，展开所有有匹配结果的课程
				this.filteredAttendanceData.forEach(courseGroup => {
					if (courseGroup.students.length > 0) {
						const index = this.collapsedCourses.indexOf(courseGroup.courseValue)
						if (index > -1) {
							this.collapsedCourses.splice(index, 1)
						}
					}
				})
			} else {
				// 没有搜索关键词且无筛选条件时，折叠所有课程
				this.attendanceData.forEach(courseGroup => {
					if (!this.collapsedCourses.includes(courseGroup.courseValue)) {
						this.collapsedCourses.push(courseGroup.courseValue)
					}
				})
			}
		},

		// 切换课程折叠状态
		toggleCourseCollapse(courseValue) {
			const index = this.collapsedCourses.indexOf(courseValue)
			if (index > -1) {
				this.collapsedCourses.splice(index, 1)
			} else {
				this.collapsedCourses.push(courseValue)
			}
		},

		// 判断课程是否折叠
		isCourseCollapsed(courseValue) {
			return this.collapsedCourses.includes(courseValue)
		},

		// 显示学生操作选项
		showStudentActions(student) {
			this.currentStudent = student
			uni.showActionSheet({
				itemList: ['确认签到', '查看详情'],
				success: (res) => {
					if (res.tapIndex === 0) {
						this.confirmStudentCheckin(student)
					} else if (res.tapIndex === 1) {
						this.showAttendanceDetail(student)
					}
				}
			})
		},

		// 确认学生签到
		async confirmStudentCheckin(student) {
			// TODO: 实现托管考勤签到逻辑
			toast(`${student.name} 托管签到功能开发中...`)
		},

		// 显示考勤详情
		showAttendanceDetail(student) {
			const details = [
				`姓名：${student.name}`,
				`签到状态：${student.isCheckedIn ? '已签到' : '未签到'}`,
				student.checkInTime ? `签到时间：${student.checkInTime}` : '',
				student.checkOutTime ? `签退时间：${student.checkOutTime}` : ''
			].filter(item => item).join('\n')

			uni.showModal({
				title: '考勤详情',
				content: details,
				showCancel: false
			})
		},

		// 课程筛选变化
		onCourseChange(e) {
			const index = e.detail.value
			this.selectedCourseIndex = index
			if (index === 0) {
				this.selectedCourse = ''
			} else {
				this.selectedCourse = this.courseOptions[index].value
			}
		},

		// 状态筛选变化
		onStatusChange(e) {
			const index = e.detail.value
			this.selectedStatusIndex = index
			this.selectedStatus = this.statusOptions[index].value
		},

		// 搜索输入处理
		onSearchInput(e) {
			this.searchKeyword = e.detail.value
		},

		// 切换学生选择状态
		toggleStudentSelection(student) {
			const index = this.selectedStudents.indexOf(student.id)
			if (index > -1) {
				this.selectedStudents.splice(index, 1)
			} else {
				this.selectedStudents.push(student.id)
			}
		},

		// 批量签到
		handleBatchCheckin() {
			if (this.selectedStudents.length === 0) {
				toast('请先选择学生')
				return
			}
			// TODO: 实现批量签到逻辑
			toast(`批量签到功能开发中，已选择${this.selectedStudents.length}名学生`)
		},

		// 批量确认
		handleBatchConfirm() {
			if (this.selectedStudents.length === 0) {
				toast('请先选择学生')
				return
			}
			// TODO: 实现批量确认逻辑
			toast(`批量确认功能开发中，已选择${this.selectedStudents.length}名学生`)
		},

		// 导出数据
		handleExport() {
			// TODO: 实现导出功能
			toast('导出功能开发中...')
		},

		// 确认单个考勤
		confirmSingleAttendance(student) {
			// TODO: 实现单个确认逻辑
			toast(`确认${student.name}的考勤记录`)
		},

		// 获取状态样式类
		getStatusClass(status) {
			const classMap = {
				present: 'present',
				absent: 'absent',
				leave: 'leave'
			}
			return classMap[status] || 'absent'
		},

		// 获取状态图标
		getStatusIcon(status) {
			const iconMap = {
				present: '✅',
				absent: '❌',
				leave: '🏠'
			}
			return iconMap[status] || '❌'
		},

		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				present: '已到课',
				absent: '未到课'
			}
			return statusMap[status] || '未知'
		}
	}
}
</script>

<style lang="scss" scoped>
.custody-attendance-container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 自定义导航栏 */
.custom-navbar {
	background: #ffffff;
	padding-top: var(--status-bar-height);
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.navbar-content {
	height: 88rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 32rpx;
}

.navbar-left {
	width: 80rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: flex-start;
}

.back-icon {
	font-size: 36rpx;
	color: #333333;
}

.navbar-title {
	flex: 1;
	display: flex;
	justify-content: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #333333;
}

.navbar-right {
	width: 160rpx;
	display: flex;
	justify-content: flex-end;
}

.date-selector {
	display: flex;
	align-items: center;
	gap: 8rpx;
	padding: 12rpx 16rpx;
	background: rgba(102, 126, 234, 0.1);
	border-radius: 20rpx;
}

.date-text {
	font-size: 24rpx;
	color: #667eea;
	font-weight: 500;
}

.calendar-icon {
	font-size: 24rpx;
}

/* 统计区域 */
.stats-section {
	display: flex;
	gap: 16rpx;
	padding: 30rpx 32rpx;
	background: #ffffff;
}

.stat-card {
	flex: 1;
	padding: 30rpx 20rpx;
	border-radius: 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
	position: relative;
	overflow: hidden;
	cursor: pointer;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
	}

	&.active {
		transform: scale(1.05);
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
	}

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 6rpx;
	}

	&.total {
		background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
		&::before { background: linear-gradient(90deg, #2196f3 0%, #1976d2 100%); }
		
		&.active {
			background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
		}
	}

	&.checked-in {
		background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
		&::before { background: linear-gradient(90deg, #4caf50 0%, #388e3c 100%); }
		
		&.active {
			background: linear-gradient(135deg, #c8e6c9 0%, #a5d6a7 100%);
		}
	}

	&.not-checked-in {
		background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
		&::before { background: linear-gradient(90deg, #ff9800 0%, #f57c00 100%); }
		
		&.active {
			background: linear-gradient(135deg, #ffe0b2 0%, #ffcc80 100%);
		}
	}

	&.present {
		background: linear-gradient(135deg, #e1f5fe 0%, #b3e5fc 100%);
		&::before { background: linear-gradient(90deg, #03a9f4 0%, #0288d1 100%); }
		
		&.active {
			background: linear-gradient(135deg, #b3e5fc 0%, #81d4fa 100%);
		}
	}

	&.absent {
		background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
		&::before { background: linear-gradient(90deg, #f44336 0%, #d32f2f 100%); }
		
		&.active {
			background: linear-gradient(135deg, #ffcdd2 0%, #ef9a9a 100%);
		}
	}
}

.stat-icon {
	font-size: 48rpx;
}

.stat-info {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

.stat-number {
	font-size: 48rpx;
	font-weight: 700;
	color: #333333;
	line-height: 1;
}

.stat-label {
	font-size: 24rpx;
	color: #666666;
	font-weight: 500;
}

/* 搜索栏样式 */
.search-section {
	margin: 30rpx;
	margin-bottom: 20rpx;
}

.search-box {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 24rpx 30rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;

	&:focus-within {
		border-color: rgba(102, 126, 234, 0.3);
		box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.15);
	}
}

.search-icon {
	font-size: 32rpx;
	color: #999999;
	margin-right: 16rpx;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333333;
	border: none;
	outline: none;
}

.clear-icon {
	font-size: 32rpx;
	color: #999999;
	margin-left: 16rpx;
	cursor: pointer;
}

/* 加载和空状态 */
.loading-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;
}

.loading-text {
	font-size: 28rpx;
	color: #999999;
}

.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;
	gap: 32rpx;
}

.empty-icon {
	font-size: 120rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 32rpx;
	color: #999999;
	text-align: center;
}

.empty-action {
	padding: 20rpx 40rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 40rpx;
}

.action-text {
	font-size: 28rpx;
	color: #ffffff;
	font-weight: 500;
}

/* 考勤列表 */
.attendance-list {
	padding: 0 32rpx 120rpx;
}

.course-group {
	margin-bottom: 32rpx;
}

.course-header {
	background: #ffffff;
	padding: 32rpx;
	border-radius: 20rpx 20rpx 0 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.course-info {
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.course-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.course-count {
	font-size: 24rpx;
	color: #999999;
}

.collapse-icon {
	transition: transform 0.3s ease;
	font-size: 24rpx;
	color: #999999;
	
	&.collapsed {
		transform: rotate(-90deg);
	}
}

.students-list {
	background: #ffffff;
	border-radius: 0 0 20rpx 20rpx;
	overflow: hidden;
}

.student-card {
	padding: 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.3s ease;

	&:last-child {
		border-bottom: none;
	}

	&:active {
		background-color: #f8f9fa;
	}
}

.student-header {
	display: flex;
	align-items: center;
	gap: 24rpx;
	margin-bottom: 20rpx;
}

.student-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
}

.avatar-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
}

.student-basic-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.student-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.student-course {
	font-size: 24rpx;
	color: #999999;
}

.student-status {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	
	&.present {
		background: rgba(76, 175, 80, 0.1);
	}
	
	&.absent {
		background: rgba(244, 67, 54, 0.1);
	}
}

.status-text {
	font-size: 24rpx;
	font-weight: 500;
	
	.student-status.present & {
		color: #4caf50;
	}
	
	.student-status.absent & {
		color: #f44336;
	}
}

/* 签到状态样式 */
.checkin-status {
	display: flex;
	align-items: center;
	padding: 12rpx 16rpx;
	margin-bottom: 16rpx;
	border-radius: 12rpx;
	font-size: 24rpx;
	font-weight: 500;

	.status-icon {
		margin-right: 8rpx;
		font-size: 28rpx;
	}

	.status-text {
		flex: 1;
	}

	&.checked-in {
		background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
		color: #155724;
		border: 1rpx solid #c3e6cb;
	}

	&.not-checked-in {
		background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
		color: #721c24;
		border: 1rpx solid #f5c6cb;
	}
}

/* 时间信息 */
.card-content {
	margin-top: 16rpx;
}

.time-info {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.time-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.time-icon {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;
	
	&.checkin {
		background: rgba(76, 175, 80, 0.1);
	}
	
	&.checkout {
		background: rgba(255, 152, 0, 0.1);
	}
	
	&.absent {
		background: rgba(244, 67, 54, 0.1);
	}
}

.time-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.time-label {
	font-size: 24rpx;
	color: #999999;
}

.time-value {
	font-size: 28rpx;
	font-weight: 500;
	color: #333333;
	
	&.absent {
		color: #f44336;
	}
}
</style>

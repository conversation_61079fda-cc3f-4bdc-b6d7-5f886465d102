<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">托管考勤管理</text>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<view class="loading-content">
				<view class="loading-spinner"></view>
				<text class="loading-text">加载中...</text>
			</view>
		</view>

		<!-- 日期和统计卡片 -->
		<view class="date-stats-card">
			<view class="date-section">
				<view class="date-nav">
					<view class="date-btn" @click="changeDate(-1)">
						<u-icon name="arrow-left" color="#667eea" size="16"></u-icon>
					</view>
					<view class="current-date" @click="showDatePickerDialog">
						<text class="date-text">{{ currentDate }}</text>
						<u-icon name="calendar" color="#667eea" size="16" class="calendar-icon"></u-icon>
					</view>
					<view class="date-btn" @click="changeDate(1)">
						<u-icon name="arrow-right" color="#667eea" size="16"></u-icon>
					</view>
				</view>
			</view>

			<view class="stats-section">
				<view
					class="stat-card total"
					:class="{ active: currentFilter === 'all' }"
					@click="setFilter('all')"
				>
					<view class="stat-icon">👥</view>
					<view class="stat-info">
						<text class="stat-number">{{ totalStudents }}</text>
						<text class="stat-label">总人数</text>
					</view>
				</view>
				<view
					class="stat-card checked-in"
					:class="{ active: currentFilter === 'checked-in' }"
					@click="setFilter('checked-in')"
				>
					<view class="stat-icon">✅</view>
					<view class="stat-info">
						<text class="stat-number">{{ checkedInCount }}</text>
						<text class="stat-label">已签到</text>
					</view>
				</view>
				<view
					class="stat-card not-checked-in"
					:class="{ active: currentFilter === 'not-checked-in' }"
					@click="setFilter('not-checked-in')"
				>
					<view class="stat-icon">⏰</view>
					<view class="stat-info">
						<text class="stat-number">{{ notCheckedInCount }}</text>
						<text class="stat-label">未签到</text>
					</view>
				</view>
				<view
					class="stat-card present"
					:class="{ active: currentFilter === 'present' }"
					@click="setFilter('present')"
				>
					<view class="stat-icon">🏫</view>
					<view class="stat-info">
						<text class="stat-number">{{ presentCount }}</text>
						<text class="stat-label">已到课</text>
					</view>
				</view>
				<view
					class="stat-card absent"
					:class="{ active: currentFilter === 'absent' }"
					@click="setFilter('absent')"
				>
					<view class="stat-icon">❌</view>
					<view class="stat-info">
						<text class="stat-number">{{ absentCount }}</text>
						<text class="stat-label">未到课</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 搜索栏 -->
		<view class="search-section">
			<view class="search-box">
				<text class="search-icon">🔍</text>
				<input
					class="search-input"
					type="text"
					placeholder="请输入学生姓名"
					v-model="searchKeyword"
					@input="handleSearch"
				/>
				<view v-if="searchKeyword" class="clear-icon" @click="clearSearch">
					<text>×</text>
				</view>
			</view>
		</view>

		<!-- 课程筛选 -->
		<view class="filter-section">
			<view class="filter-tabs">
				<view
					v-for="course in courseFilter"
					:key="course.value"
					class="filter-tab"
					:class="{ active: selectedCourse === course.value }"
					@click="filterByCourse(course.value)"
				>
					<text class="tab-text">{{ course.label }}</text>
				</view>
			</view>
		</view>

		<!-- 批量操作栏 -->
		<view v-if="isSelectionMode" class="batch-operation-bar">
			<view class="batch-info">
				<text class="batch-text">已选择 {{ selectedStudents.length }} 名学生</text>
			</view>
			<view class="batch-actions">
				<view class="batch-btn cancel" @click="cancelSelection">
					<text class="btn-text">取消</text>
				</view>
				<view class="batch-btn confirm" @click="batchCheckin">
					<text class="btn-text">批量签到</text>
				</view>
			</view>
		</view>

		<!-- 错误状态 -->
		<view v-if="errorMessage && attendanceData.length === 0" class="error-state">
			<view class="error-icon">⚠️</view>
			<text class="error-text">{{ errorMessage }}</text>
			<view class="error-actions">
				<view class="error-action" @click="loadAttendanceData">
					<text class="action-text">重新加载</text>
				</view>
				<view class="error-action secondary" @click="refreshData">
					<text class="action-text">刷新页面</text>
				</view>
			</view>
		</view>

		<!-- 搜索栏 -->
		<view class="search-section">
			<view class="search-box">
				<text class="search-icon">🔍</text>
				<input 
					class="search-input" 
					type="text" 
					placeholder="请输入学生姓名"
					v-model="searchKeyword"
					@input="handleSearch"
				/>
				<view v-if="searchKeyword" class="clear-icon" @click="clearSearch">
					<text>×</text>
				</view>
			</view>
		</view>

		<!-- 空状态 -->
		<view v-else-if="attendanceData.length === 0" class="empty-state">
			<view class="empty-icon">📋</view>
			<text class="empty-text">暂无考勤数据</text>
			<view class="empty-action" @click="loadAttendanceData">
				<text class="action-text">点击重新加载</text>
			</view>
		</view>

		<view v-for="courseGroup in filteredAttendanceData" :key="courseGroup.courseName" class="course-section">
			<!-- 课程头部 -->
			<view class="course-header" @click="toggleCourseCollapse(courseGroup.courseName)">
				<view class="course-info">
					<view class="course-title">
						<text class="course-name">{{ courseGroup.courseName }}</text>
						<text class="course-count">{{ courseGroup.students.length }}人</text>
					</view>
					<view class="course-stats">
						<text class="stat-item present">已到课: {{ courseGroup.presentCount || 0 }}</text>
						<text class="stat-item absent">未到课: {{ courseGroup.absentCount || 0 }}</text>
					</view>
				</view>
				<view class="collapse-icon" :class="{ collapsed: isCourseCollapsed(courseGroup.courseName) }">
					<u-icon :name="isCourseCollapsed(courseGroup.courseName) ? 'arrow-right' : 'arrow-down'" color="#ffffff" size="16"></u-icon>
				</view>
			</view>

			<!-- 学生列表 -->
			<view v-if="!isCourseCollapsed(courseGroup.courseName)" class="students-list">
				<view v-for="student in courseGroup.students" :key="student.studentId"
					class="student-card"
					:class="{
						'selected': selectedStudents.includes(student.studentId),
						'checked-in': student.isCheckedIn,
						'present': student.attendanceStatus === 'present',
						'absent': student.attendanceStatus === 'absent'
					}"
					@click="handleStudentClick(student)"
					@longpress="toggleStudentSelection(student)"
				>
					<!-- 选择框 (仅在选择模式下显示) -->
					<view v-if="isSelectionMode" class="selection-checkbox" :class="{ 'checked': selectedStudents.includes(student.studentId) }">
						<u-icon v-if="selectedStudents.includes(student.studentId)" name="checkmark" color="#ffffff" size="14"></u-icon>
					</view>

					<!-- 学生头像和基本信息 -->
					<view class="student-header">
						<view class="student-avatar">
							<text class="avatar-text">{{ student.studentName.charAt(0) }}</text>
						</view>
						<view class="student-basic-info">
							<text class="student-name">{{ student.studentName }}</text>
							<text class="student-class">{{ student.className || '未分班' }}</text>
						</view>
						<view class="student-status" :class="student.attendanceStatus">
							<text class="status-text">{{ getStatusText(student.attendanceStatus) }}</text>
						</view>
					</view>

					<!-- 签到状态标识 -->
					<view class="checkin-status" :class="student.isCheckedIn ? 'checked-in' : 'not-checked-in'">
						<view class="status-icon">{{ student.isCheckedIn ? '✅' : '⏰' }}</view>
						<text class="status-text">{{ student.isCheckedIn ? '已签到' : '未签到' }}</text>
					</view>

					<view class="card-content">
						<view class="time-info">
							<!-- 签到时间 -->
							<view class="time-item" v-if="student.checkInTime">
								<view class="time-icon checkin">✅</view>
								<view class="time-details">
									<text class="time-label">签到时间</text>
									<text class="time-value">{{ student.checkInTime }}</text>
								</view>
							</view>

							<!-- 签退时间 -->
							<view class="time-item" v-if="student.checkOutTime">
								<view class="time-icon checkout">🌇</view>
								<view class="time-details">
									<text class="time-label">签退时间</text>
									<text class="time-value">{{ student.checkOutTime }}</text>
								</view>
							</view>

							<!-- 未签到状态 -->
							<view class="time-item" v-if="!student.checkInTime && student.attendanceStatus === 'absent'">
								<view class="time-icon absent">⏰</view>
								<view class="time-details">
									<text class="time-label">状态</text>
									<text class="time-value absent">未到课</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 浮动操作按钮 -->
		<view class="floating-action-btn" @click="toggleSelectionMode">
			<u-icon :name="isSelectionMode ? 'close' : 'plus'" color="#ffffff" size="24"></u-icon>
		</view>

		<!-- 日期选择器 -->
		<CustomDatePicker
			v-if="showDatePicker"
			:value="pickerValue"
			@confirm="onDateConfirm"
			@cancel="onDateCancel"
		/>
	</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'
// 暂时注释掉API导入，使用模拟数据
// import {
// 	getCustodyAttendanceOverview,
// 	custodyCheckin,
// 	custodyLeave,
// 	batchCustodyCheckin,
// 	batchCustodyLeave
// } from '@/api/custodyAttendance.js'
import CustomDatePicker from '@/components/CustomDatePicker/CustomDatePicker.vue'

export default {
	components: {
		CustomDatePicker
	},
	data() {
		return {
			loading: false,
			currentDate: '',
			currentDateValue: '', // 用于API请求的日期格式
			selectedCourse: 'all',
			isSelectionMode: false,
			selectedStudents: [],
			totalStudents: 0,
			checkedInCount: 0,      // 已签到
			notCheckedInCount: 0,   // 未签到
			presentCount: 0,        // 已到课
			absentCount: 0,         // 未到课
			// 筛选相关
			currentFilter: 'all',   // 当前筛选条件
			courseFilter: [
				{ label: '全部', value: 'all' }
			],
			attendanceData: [],
			originalAttendanceData: [], // 保存原始数据用于筛选
			courseList: [], // 课程列表
			errorMessage: '', // 错误信息
			retryCount: 0, // 重试次数
			maxRetries: 3, // 最大重试次数
			collapsedCourses: [], // 折叠的课程名称数组
			// 搜索相关
			searchKeyword: '', // 搜索关键词
			searchTimer: null, // 搜索防抖定时器
			// 日期选择器相关
			showDatePicker: false,
			pickerValue: [],
			// UI状态
			currentStudent: null
		}
	},
	
	computed: {
		filteredAttendanceData() {
			let data = this.attendanceData

			// 状态筛选
			if (this.currentFilter !== 'all') {
				data = data.map(courseGroup => {
					const filteredStudents = courseGroup.students.filter(student => {
						switch (this.currentFilter) {
							case 'checked-in':
								return student.isCheckedIn
							case 'not-checked-in':
								return !student.isCheckedIn
							case 'present':
								return student.attendanceStatus === 'present'
							case 'absent':
								return student.attendanceStatus === 'absent'
							default:
								return true
						}
					})

					if (filteredStudents.length > 0) {
						return {
							...courseGroup,
							students: filteredStudents
						}
					}
					return null
				}).filter(courseGroup => courseGroup !== null)
			}

			// 课程筛选
			if (this.selectedCourse !== 'all') {
				data = data.filter(courseGroup => courseGroup.courseName === this.selectedCourse)
			}

			// 搜索筛选
			if (this.searchKeyword.trim()) {
				const keyword = this.searchKeyword.trim().toLowerCase()
				data = data.map(courseGroup => {
					const filteredStudents = courseGroup.students.filter(student =>
						student.studentName.toLowerCase().includes(keyword)
					)

					if (filteredStudents.length > 0) {
						return {
							...courseGroup,
							students: filteredStudents
						}
					}
					return null
				}).filter(courseGroup => courseGroup !== null)
			}

			return data
		}
	},

	onLoad() {
		this.initCurrentDate()
		this.initDatePicker()
		this.loadAttendanceData()
	},

	// 下拉刷新
	onPullDownRefresh() {
		this.refreshData()
	},

	// 页面显示时刷新数据
	onShow() {
		// 如果数据已加载过，则静默刷新
		if (this.attendanceData.length > 0) {
			this.loadAttendanceData(false)
		}
		// 确保默认折叠状态
		this.updateCourseCollapseState()
	},
	
	methods: {
		// 初始化当前日期
		initCurrentDate() {
			const today = new Date()
			const year = today.getFullYear()
			const month = String(today.getMonth() + 1).padStart(2, '0')
			const day = String(today.getDate()).padStart(2, '0')
			const weekdays = ['日', '一', '二', '三', '四', '五', '六']
			const weekday = weekdays[today.getDay()]

			this.currentDate = `${year}年${month}月${day}日 周${weekday}`
			this.currentDateValue = `${year}-${month}-${day}`
		},

		// 初始化日期选择器
		initDatePicker() {
			const today = new Date()
			this.pickerValue = [
				today.getFullYear() - 1990, // 年份索引
				today.getMonth(), // 月份索引
				today.getDate() - 1 // 日期索引
			]
		},

		// 显示日期选择器
		showDatePickerDialog() {
			this.showDatePicker = true
		},

		// 日期选择确认
		onDateConfirm(e) {
			const dateInfo = e.value
			this.currentDate = dateInfo.label
			this.currentDateValue = dateInfo.value
			this.pickerValue = e.value
			this.showDatePicker = false

			// 重新加载数据
			this.loadAttendanceData()
		},

		// 日期选择取消
		onDateCancel() {
			this.showDatePicker = false
		},

		// 切换日期
		changeDate(days) {
			const currentDate = new Date(this.currentDateValue)
			currentDate.setDate(currentDate.getDate() + days)

			const year = currentDate.getFullYear()
			const month = String(currentDate.getMonth() + 1).padStart(2, '0')
			const day = String(currentDate.getDate()).padStart(2, '0')
			const weekdays = ['日', '一', '二', '三', '四', '五', '六']
			const weekday = weekdays[currentDate.getDay()]

			this.currentDate = `${year}年${month}月${day}日 周${weekday}`
			this.currentDateValue = `${year}-${month}-${day}`

			// 重新加载数据
			this.loadAttendanceData()
		},

		// 加载考勤数据
		async loadAttendanceData(shouldRerender = true) {
			try {
				if (shouldRerender) {
					this.loading = true
					this.errorMessage = ''
				}

				// 使用模拟数据
				const mockData = [
					{
						studentId: 1,
						studentName: '张小明',
						className: '大班A',
						courseName: '数学辅导',
						checkInTime: '14:30',
						checkOutTime: '',
						attendanceStatus: 'present',
						attendanceId: 1
					},
					{
						studentId: 2,
						studentName: '李小红',
						className: '大班A',
						courseName: '数学辅导',
						checkInTime: '',
						checkOutTime: '',
						attendanceStatus: 'absent',
						attendanceId: 2
					},
					{
						studentId: 3,
						studentName: '王小华',
						className: '中班B',
						courseName: '英语辅导',
						checkInTime: '15:00',
						checkOutTime: '17:00',
						attendanceStatus: 'present',
						attendanceId: 3
					},
					{
						studentId: 4,
						studentName: '赵小美',
						className: '中班B',
						courseName: '英语辅导',
						checkInTime: '',
						checkOutTime: '',
						attendanceStatus: 'absent',
						attendanceId: 4
					},
					{
						studentId: 5,
						studentName: '刘小强',
						className: '小班C',
						courseName: '美术辅导',
						checkInTime: '16:00',
						checkOutTime: '',
						attendanceStatus: 'present',
						attendanceId: 5
					}
				]

				// 模拟API响应延迟
				await new Promise(resolve => setTimeout(resolve, 500))

				this.originalAttendanceData = this.formatAttendanceData(mockData)
				this.filterAttendanceData()
				this.calculateStats()
				this.updateCourseFilter()
				this.retryCount = 0 // 重置重试次数

				// 数据加载完成后，确保折叠状态正确
				this.$nextTick(() => {
					this.updateCourseCollapseState()
				})

			} catch (error) {
				console.error('加载考勤数据失败:', error)
				this.errorMessage = this.getErrorMessage(error)

				if (shouldRerender) {
					toast(this.errorMessage)
				}
			} finally {
				if (shouldRerender) {
					this.loading = false
				}
			}
		},

		// 格式化考勤数据
		formatAttendanceData(data) {
			if (!data || !Array.isArray(data)) {
				return []
			}
			
			const courseMap = new Map()

			data.forEach(item => {
				const courseId = item.courseId
				const courseName = item.courseName || '未分配课程'

				if (!courseMap.has(courseId)) {
					courseMap.set(courseId, {
						courseName: courseName,
						courseValue: String(courseId),
						students: []
					})
					
					// 默认所有课程都折叠
					const courseValue = String(courseId)
					if (!this.collapsedCourses.includes(courseValue)) {
						this.collapsedCourses.push(courseValue)
					}
				}

				// 处理学生数据
				item.students.forEach(student => {
					const formattedStudent = {
						id: student.id,
						name: student.name,
						checkInTime: student.checkInTime || '',
						checkOutTime: student.checkOutTime || '',
						status: student.status || 'absent',
						isCheckedIn: !!student.checkInTime,
						isConfirmed: student.isConfirmed || false,
						attendanceId: student.attendanceId || null
					}

					courseMap.get(courseId).students.push(formattedStudent)
				})
			})

			return Array.from(courseMap.values())
		},

		// 过滤考勤数据
		filterAttendanceData() {
			this.attendanceData = this.originalAttendanceData
		},

		// 计算统计数据
		calculateStats() {
			let total = 0
			let checkedIn = 0
			let notCheckedIn = 0
			let present = 0
			let absent = 0

			// 始终基于原始数据计算统计，不受筛选影响
			this.originalAttendanceData.forEach(courseGroup => {
				courseGroup.students.forEach(student => {
					total++

					if (student.isCheckedIn) {
						checkedIn++
					} else {
						notCheckedIn++
					}

					if (student.status === 'present') {
						present++
					} else if (student.status === 'absent') {
						absent++
					}
				})
			})

			this.totalStudents = total
			this.checkedInCount = checkedIn
			this.notCheckedInCount = notCheckedIn
			this.presentCount = present
			this.absentCount = absent
		},

		// 更新课程筛选选项
		updateCourseFilter() {
			const courseFilter = [{ label: '全部', value: 'all' }]
			this.originalAttendanceData.forEach(courseGroup => {
				courseFilter.push({
					label: courseGroup.courseName,
					value: courseGroup.courseName
				})
			})
			this.courseFilter = courseFilter
		},

		// 获取错误信息
		getErrorMessage(error) {
			if (this.isNetworkError(error)) {
				return '网络连接失败，请检查网络设置'
			}
			return error.message || '加载数据失败，请重试'
		},

		// 判断是否为网络错误
		isNetworkError(error) {
			return error.message && (
				error.message.includes('Network') ||
				error.message.includes('timeout') ||
				error.message.includes('网络')
			)
		},

		// 设置筛选条件
		setFilter(filter) {
			this.currentFilter = filter
		},

		// 按课程筛选
		filterByCourse(courseValue) {
			this.selectedCourse = courseValue
		},

		// 搜索处理
		handleSearch(e) {
			this.searchKeyword = e.detail.value
			// 可以添加防抖逻辑
			if (this.searchTimer) {
				clearTimeout(this.searchTimer)
			}
			this.searchTimer = setTimeout(() => {
				// 搜索逻辑在computed中处理
			}, 300)
		},

		// 清除搜索
		clearSearch() {
			this.searchKeyword = ''
		},

		// 切换选择模式
		toggleSelectionMode() {
			this.isSelectionMode = !this.isSelectionMode
			if (!this.isSelectionMode) {
				this.selectedStudents = []
			}
		},

		// 取消选择
		cancelSelection() {
			this.isSelectionMode = false
			this.selectedStudents = []
		},

		// 处理学生点击
		handleStudentClick(student) {
			if (this.isSelectionMode) {
				this.toggleStudentSelection(student)
			} else {
				this.showStudentActions(student)
			}
		},

		// 切换学生选择状态
		toggleStudentSelection(student) {
			const studentId = student.studentId
			const index = this.selectedStudents.indexOf(studentId)
			if (index > -1) {
				this.selectedStudents.splice(index, 1)
			} else {
				this.selectedStudents.push(studentId)
			}
		},

		// 显示学生操作菜单
		showStudentActions(student) {
			this.currentStudent = student
			const actions = []

			if (!student.isCheckedIn) {
				actions.push('签到')
			}
			if (student.isCheckedIn && !student.checkOutTime) {
				actions.push('签退')
			}
			actions.push('查看详情')

			uni.showActionSheet({
				itemList: actions,
				success: (res) => {
					const action = actions[res.tapIndex]
					switch (action) {
						case '签到':
							this.confirmStudentCheckin(student)
							break
						case '签退':
							this.confirmStudentCheckout(student)
							break
						case '查看详情':
							this.showStudentDetail(student)
							break
					}
				}
			})
		},

		// 切换课程折叠状态
		toggleCourseCollapse(courseName) {
			const index = this.collapsedCourses.indexOf(courseName)
			if (index > -1) {
				this.collapsedCourses.splice(index, 1)
			} else {
				this.collapsedCourses.push(courseName)
			}
		},

		// 检查课程是否折叠
		isCourseCollapsed(courseName) {
			return this.collapsedCourses.includes(courseName)
		},

		// 更新课程折叠状态
		updateCourseCollapseState() {
			// 默认折叠所有课程
			this.collapsedCourses = this.attendanceData.map(course => course.courseName)
		},

		// 返回上一页
		goBack() {
			uni.navigateBack()
		},

		// 刷新数据
		refreshData() {
			this.loadAttendanceData().finally(() => {
				uni.stopPullDownRefresh()
			})
		},

		// 打开日期选择器
		openDatePicker() {
			uni.showModal({
				title: '选择日期',
				content: '请使用日期选择器选择要查看的考勤日期',
				confirmText: '今天',
				cancelText: '取消',
				success: (res) => {
					if (res.confirm) {
						this.initCurrentDate()
						this.loadAttendanceData()
					}
				}
			})
		},

		// 设置日期
		setDateAndRefresh(date) {
			const dateObj = new Date(date)
			this.currentDate = dateObj.toLocaleDateString('zh-CN', {
				month: '2-digit',
				day: '2-digit'
			})
			this.currentDateValue = date
			this.loadAttendanceData()
		},

		// 设置筛选条件
		setFilter(filterType) {
			this.currentFilter = filterType
			this.updateCourseCollapseState()
		},

		// 搜索处理
		handleSearch() {
			if (this.searchTimer) {
				clearTimeout(this.searchTimer)
			}

			this.searchTimer = setTimeout(() => {
				this.updateCourseCollapseState()
				this.searchTimer = null
			}, 300)
		},

		// 清除搜索
		clearSearch() {
			this.searchKeyword = ''
			this.updateCourseCollapseState()
		},

		// 更新课程折叠状态
		updateCourseCollapseState() {
			if (this.searchKeyword.trim() || this.currentFilter !== 'all') {
				// 有搜索关键词或筛选条件时，展开所有有匹配结果的课程
				this.filteredAttendanceData.forEach(courseGroup => {
					if (courseGroup.students.length > 0) {
						const index = this.collapsedCourses.indexOf(courseGroup.courseValue)
						if (index > -1) {
							this.collapsedCourses.splice(index, 1)
						}
					}
				})
			} else {
				// 没有搜索关键词且无筛选条件时，折叠所有课程
				this.attendanceData.forEach(courseGroup => {
					if (!this.collapsedCourses.includes(courseGroup.courseValue)) {
						this.collapsedCourses.push(courseGroup.courseValue)
					}
				})
			}
		},

		// 切换课程折叠状态
		toggleCourseCollapse(courseValue) {
			const index = this.collapsedCourses.indexOf(courseValue)
			if (index > -1) {
				this.collapsedCourses.splice(index, 1)
			} else {
				this.collapsedCourses.push(courseValue)
			}
		},

		// 判断课程是否折叠
		isCourseCollapsed(courseValue) {
			return this.collapsedCourses.includes(courseValue)
		},

		// 显示学生操作选项
		showStudentActions(student) {
			this.currentStudent = student
			uni.showActionSheet({
				itemList: ['确认签到', '查看详情'],
				success: (res) => {
					if (res.tapIndex === 0) {
						this.confirmStudentCheckin(student)
					} else if (res.tapIndex === 1) {
						this.showAttendanceDetail(student)
					}
				}
			})
		},

		// 确认学生签到
		async confirmStudentCheckin(student) {
			try {
				uni.showLoading({ title: '签到中...' })

				// 模拟API调用
				await new Promise(resolve => setTimeout(resolve, 1000))

				// 模拟成功响应
				toast(`${student.studentName} 签到成功`)
				this.loadAttendanceData(false)
			} catch (error) {
				console.error('签到失败:', error)
				toast('签到失败，请重试')
			} finally {
				uni.hideLoading()
			}
		},

		// 确认学生签退
		async confirmStudentCheckout(student) {
			try {
				uni.showLoading({ title: '签退中...' })

				// 模拟API调用
				await new Promise(resolve => setTimeout(resolve, 1000))

				// 模拟成功响应
				toast(`${student.studentName} 签退成功`)
				this.loadAttendanceData(false)
			} catch (error) {
				console.error('签退失败:', error)
				toast('签退失败，请重试')
			} finally {
				uni.hideLoading()
			}
		},

		// 批量签到
		async batchCheckin() {
			if (this.selectedStudents.length === 0) {
				toast('请先选择学生')
				return
			}

			try {
				uni.showLoading({ title: '批量签到中...' })

				// 模拟API调用
				await new Promise(resolve => setTimeout(resolve, 1500))

				// 模拟成功响应
				toast(`批量签到成功，共${this.selectedStudents.length}名学生`)
				this.selectedStudents = []
				this.isSelectionMode = false
				this.loadAttendanceData(false)
			} catch (error) {
				console.error('批量签到失败:', error)
				toast('批量签到失败，请重试')
			} finally {
				uni.hideLoading()
			}
		},

		// 显示学生详情
		showStudentDetail(student) {
			uni.navigateTo({
				url: `/pages/admin/custody/attendance/detail?studentId=${student.studentId}&date=${this.currentDateValue}`
			})
		},



		// 获取状态文本
		getStatusText(status) {
			const statusMap = {
				present: '已到课',
				absent: '未到课'
			}
			return statusMap[status] || '未知'
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding-top: var(--status-bar-height);
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.header-content {
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 32rpx;
	position: relative;
}

.nav-left {
	position: absolute;
	left: 32rpx;
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
}

.header-title {
	flex: 1;
	display: flex;
	justify-content: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 加载状态 */
.loading-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;
}

.loading-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 24rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #667eea;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.loading-text {
	font-size: 28rpx;
	color: #999999;
}

/* 日期和统计卡片 */
.date-stats-card {
	background: #ffffff;
	margin: 30rpx;
	border-radius: 20rpx;
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.15);
	overflow: hidden;
}

.date-section {
	padding: 32rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.date-nav {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.date-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		background: rgba(255, 255, 255, 0.3);
		transform: scale(0.95);
	}
}

.current-date {
	display: flex;
	align-items: center;
	gap: 16rpx;
	padding: 16rpx 32rpx;
	background: rgba(255, 255, 255, 0.2);
	border-radius: 40rpx;
	transition: all 0.3s ease;

	&:active {
		background: rgba(255, 255, 255, 0.3);
		transform: scale(0.98);
	}
}

.date-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
}

.calendar-icon {
	margin-left: 8rpx;
}

/* 统计区域 */
.stats-section {
	display: flex;
	gap: 16rpx;
	padding: 30rpx 32rpx;
	background: #ffffff;
}

.stat-card {
	flex: 1;
	padding: 30rpx 20rpx;
	border-radius: 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
	position: relative;
	overflow: hidden;
	cursor: pointer;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
	}

	&.active {
		transform: scale(1.05);
		box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.15);
	}

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 6rpx;
	}

	&.total {
		background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
		&::before { background: linear-gradient(90deg, #2196f3 0%, #1976d2 100%); }

		&.active {
			background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
		}
	}

	&.checked-in {
		background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
		&::before { background: linear-gradient(90deg, #4caf50 0%, #388e3c 100%); }

		&.active {
			background: linear-gradient(135deg, #c8e6c9 0%, #a5d6a7 100%);
		}
	}

	&.not-checked-in {
		background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
		&::before { background: linear-gradient(90deg, #ff9800 0%, #f57c00 100%); }

		&.active {
			background: linear-gradient(135deg, #ffe0b2 0%, #ffcc80 100%);
		}
	}

	&.present {
		background: linear-gradient(135deg, #e1f5fe 0%, #b3e5fc 100%);
		&::before { background: linear-gradient(90deg, #03a9f4 0%, #0288d1 100%); }

		&.active {
			background: linear-gradient(135deg, #b3e5fc 0%, #81d4fa 100%);
		}
	}

	&.absent {
		background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
		&::before { background: linear-gradient(90deg, #f44336 0%, #d32f2f 100%); }

		&.active {
			background: linear-gradient(135deg, #ffcdd2 0%, #ef9a9a 100%);
		}
	}
}

.stat-icon {
	font-size: 48rpx;
}

.stat-info {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
}

.stat-number {
	font-size: 48rpx;
	font-weight: 700;
	color: #333333;
	line-height: 1;
}

.stat-label {
	font-size: 24rpx;
	color: #666666;
	font-weight: 500;
}

/* 搜索栏样式 */
.search-section {
	margin: 30rpx;
	margin-bottom: 20rpx;
}

.search-box {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 24rpx 30rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;

	&:focus-within {
		border-color: rgba(102, 126, 234, 0.3);
		box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.15);
	}
}

.search-icon {
	font-size: 32rpx;
	color: #999999;
	margin-right: 16rpx;
}

.search-input {
	flex: 1;
	font-size: 28rpx;
	color: #333333;
	border: none;
	outline: none;
}

.clear-icon {
	font-size: 32rpx;
	color: #999999;
	margin-left: 16rpx;
	cursor: pointer;
}

/* 筛选标签 */
.filter-section {
	margin: 0 30rpx 20rpx;
}

.filter-tabs {
	display: flex;
	gap: 16rpx;
	overflow-x: auto;
	padding: 8rpx 0;
}

.filter-tab {
	flex-shrink: 0;
	padding: 16rpx 32rpx;
	background: #ffffff;
	border-radius: 40rpx;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s ease;

	&.active {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		transform: scale(1.05);
		box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.3);
	}

	&:active {
		transform: scale(0.95);
	}
}

.tab-text {
	font-size: 28rpx;
	font-weight: 500;
	color: #333333;

	.filter-tab.active & {
		color: #ffffff;
	}
}

/* 批量操作栏 */
.batch-operation-bar {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 24rpx 32rpx;
	margin: 0 30rpx 20rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.batch-info {
	flex: 1;
}

.batch-text {
	font-size: 28rpx;
	color: #ffffff;
	font-weight: 500;
}

.batch-actions {
	display: flex;
	gap: 16rpx;
}

.batch-btn {
	padding: 16rpx 32rpx;
	border-radius: 40rpx;
	font-size: 28rpx;
	font-weight: 500;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
	}

	&.cancel {
		background: rgba(255, 255, 255, 0.2);
		color: #ffffff;
	}

	&.confirm {
		background: #ffffff;
		color: #667eea;
	}
}

.btn-text {
	font-size: 28rpx;
	font-weight: 500;
}

/* 错误状态 */
.error-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;
	gap: 32rpx;
}

.error-icon {
	font-size: 120rpx;
	opacity: 0.6;
}

.error-text {
	font-size: 32rpx;
	color: #999999;
	text-align: center;
}

.error-actions {
	display: flex;
	gap: 24rpx;
}

.error-action {
	padding: 20rpx 40rpx;
	border-radius: 40rpx;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
	}

	&:not(.secondary) {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	}

	&.secondary {
		background: #ffffff;
		border: 2rpx solid #e0e0e0;
	}
}

.action-text {
	font-size: 28rpx;
	font-weight: 500;

	.error-action:not(.secondary) & {
		color: #ffffff;
	}

	.error-action.secondary & {
		color: #333333;
	}
}

/* 空状态 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 0;
	gap: 32rpx;
}

.empty-icon {
	font-size: 120rpx;
	opacity: 0.6;
}

.empty-text {
	font-size: 32rpx;
	color: #999999;
	text-align: center;
}

.empty-action {
	padding: 20rpx 40rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 40rpx;
}

/* 课程部分 */
.course-section {
	margin: 0 30rpx 32rpx;
}

.course-header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 32rpx;
	border-radius: 20rpx 20rpx 0 0;
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.course-info {
	flex: 1;
}

.course-title {
	display: flex;
	align-items: center;
	gap: 16rpx;
	margin-bottom: 12rpx;
}

.course-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
}

.course-count {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	background: rgba(255, 255, 255, 0.2);
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
}

.course-stats {
	display: flex;
	gap: 24rpx;
}

.stat-item {
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.9);

	&.present {
		color: #c8e6c9;
	}

	&.absent {
		color: #ffcdd2;
	}
}

.collapse-icon {
	transition: transform 0.3s ease;

	&.collapsed {
		transform: rotate(-90deg);
	}
}

/* 学生列表 */
.students-list {
	background: #ffffff;
	border-radius: 0 0 20rpx 20rpx;
	overflow: hidden;
}

.student-card {
	padding: 32rpx;
	border-bottom: 1rpx solid #f0f0f0;
	transition: background-color 0.3s ease;

	&:last-child {
		border-bottom: none;
	}

	&:active {
		background-color: #f8f9fa;
	}

	&.selected {
		background-color: rgba(102, 126, 234, 0.05);
		border-left: 6rpx solid #667eea;
	}
}

/* 选择框 */
.selection-checkbox {
	width: 40rpx;
	height: 40rpx;
	border: 2rpx solid #dcdfe6;
	border-radius: 8rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 24rpx;
	transition: all 0.3s ease;

	&.checked {
		background: #667eea;
		border-color: #667eea;
	}
}

.student-header {
	display: flex;
	align-items: center;
	gap: 24rpx;
	margin-bottom: 20rpx;
}

.student-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
}

.avatar-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
}

.student-basic-info {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 8rpx;
}

.student-name {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.student-class {
	font-size: 24rpx;
	color: #999999;
}

.student-status {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;

	&.present {
		background: rgba(76, 175, 80, 0.1);
		color: #4caf50;
	}

	&.absent {
		background: rgba(244, 67, 54, 0.1);
		color: #f44336;
	}
}

.status-text {
	font-size: 24rpx;
	font-weight: 500;
}

/* 签到状态样式 */
.checkin-status {
	display: flex;
	align-items: center;
	padding: 12rpx 16rpx;
	margin-bottom: 16rpx;
	border-radius: 12rpx;
	font-size: 24rpx;
	font-weight: 500;

	.status-icon {
		margin-right: 8rpx;
		font-size: 28rpx;
	}

	.status-text {
		flex: 1;
	}

	&.checked-in {
		background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
		color: #155724;
		border: 1rpx solid #c3e6cb;
	}

	&.not-checked-in {
		background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
		color: #721c24;
		border: 1rpx solid #f5c6cb;
	}
}

/* 时间信息 */
.card-content {
	margin-top: 16rpx;
}

.time-info {
	display: flex;
	flex-direction: column;
	gap: 16rpx;
}

.time-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.time-icon {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 24rpx;

	&.checkin {
		background: rgba(76, 175, 80, 0.1);
	}

	&.checkout {
		background: rgba(255, 152, 0, 0.1);
	}

	&.absent {
		background: rgba(244, 67, 54, 0.1);
	}
}

.time-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 4rpx;
}

.time-label {
	font-size: 24rpx;
	color: #999999;
}

.time-value {
	font-size: 28rpx;
	font-weight: 500;
	color: #333333;

	&.absent {
		color: #f44336;
	}
}

/* 浮动操作按钮 */
.floating-action-btn {
	position: fixed;
	bottom: 120rpx;
	right: 60rpx;
	width: 120rpx;
	height: 120rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.4);
	transition: all 0.3s ease;
	z-index: 100;

	&:active {
		transform: scale(0.9);
	}
}



</style>

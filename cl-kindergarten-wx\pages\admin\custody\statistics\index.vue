<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">教师课时统计</text>
				</view>
			</view>
		</view>

		<!-- 页面内容 -->
		<view class="content">
			<view class="empty-state">
				<view class="empty-icon">📊</view>
				<text class="empty-title">教师课时统计</text>
				<text class="empty-desc">此页面用于统计教师授课时间和课时数据</text>
				<text class="empty-note">功能开发中，敬请期待...</text>
			</view>
		</view>
	</view>
</template>

<script>
import {toast} from '@/utils/utils.js'

export default {
	data() {
		return {
			
		}
	},
	methods: {
		goBack() {
			uni.navigateBack()
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
	padding-top: var(--status-bar-height);
	box-shadow: 0 4rpx 20rpx rgba(255, 152, 0, 0.3);
}

.header-content {
	height: 88rpx;
	display: flex;
	align-items: center;
	padding: 0 30rpx;
	position: relative;
}

.nav-left {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		background: rgba(255, 255, 255, 0.3);
		transform: scale(0.95);
	}
}

.header-title {
	position: absolute;
	left: 50%;
	transform: translateX(-50%);
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 页面内容 */
.content {
	padding: 40rpx;
	min-height: calc(100vh - 88rpx - var(--status-bar-height));
	display: flex;
	align-items: center;
	justify-content: center;
}

.empty-state {
	text-align: center;
	padding: 80rpx 40rpx;
	background: #ffffff;
	border-radius: 24rpx;
	box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
	width: 100%;
	max-width: 600rpx;
}

.empty-icon {
	font-size: 120rpx;
	margin-bottom: 32rpx;
	display: block;
}

.empty-title {
	font-size: 40rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 16rpx;
	display: block;
}

.empty-desc {
	font-size: 28rpx;
	color: #666666;
	margin-bottom: 24rpx;
	display: block;
	line-height: 1.5;
}

.empty-note {
	font-size: 24rpx;
	color: #999999;
	display: block;
	padding: 16rpx 24rpx;
	background: #f8f9fa;
	border-radius: 12rpx;
	border-left: 4rpx solid #FF9800;
}
</style>

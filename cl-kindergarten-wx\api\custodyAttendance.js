import request from '@/utils/request.js'

// 获取托管考勤概览
export function getCustodyAttendanceOverview(params) {
  return request({
    url: '/business/custodyAttendance/overview',
    method: 'get',
    params
  })
}

// 托管签到
export function custodyCheckin(data) {
  return request({
    url: '/business/custodyAttendance/checkin',
    method: 'post',
    data
  })
}

// 托管离园
export function custodyLeave(data) {
  return request({
    url: '/business/custodyAttendance/leave',
    method: 'post',
    data
  })
}

// 批量托管签到
export function batchCustodyCheckin(data) {
  return request({
    url: '/business/custodyAttendance/batchCheckin',
    method: 'post',
    data
  })
}

// 批量托管离园
export function batchCustodyLeave(data) {
  return request({
    url: '/business/custodyAttendance/batchLeave',
    method: 'post',
    data
  })
}

// 获取托管考勤详情
export function getCustodyAttendanceDetail(params) {
  return request({
    url: '/business/custodyAttendance/detail',
    method: 'get',
    params
  })
}

// 获取托管考勤统计
export function getCustodyAttendanceStats(params) {
  return request({
    url: '/business/custodyAttendance/stats',
    method: 'get',
    params
  })
}
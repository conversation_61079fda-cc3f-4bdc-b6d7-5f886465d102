import request from '@/utils/request.js'

// 查询托管考勤列表 (对应web端的listCourseAttendance)
export function listCourseAttendance(params) {
  return request({
    url: '/business/course-attendance/list',
    method: 'get',
    params
  })
}

// 查询托管考勤详细
export function getCourseAttendance(attendanceId) {
  return request({
    url: `/business/course-attendance/${attendanceId}`,
    method: 'get'
  })
}

// 新增托管考勤
export function addCourseAttendance(data) {
  return request({
    url: '/business/course-attendance',
    method: 'post',
    data
  })
}

// 修改托管考勤
export function updateCourseAttendance(data) {
  return request({
    url: '/business/course-attendance',
    method: 'put',
    data
  })
}

// 删除托管考勤
export function delCourseAttendance(attendanceId) {
  return request({
    url: `/business/course-attendance/${attendanceId}`,
    method: 'delete'
  })
}

// 托管签到 (对应web端的courseCheckin)
export function courseCheckin(data) {
  return request({
    url: '/business/course-attendance/checkin',
    method: 'post',
    data
  })
}

// 确认单个考勤记录
export function confirmSingleAttendance(attendanceId) {
  return request({
    url: `/business/course-attendance/confirm/${attendanceId}`,
    method: 'post'
  })
}

// 批量确认考勤记录
export function confirmCourseAttendance(attendanceIds) {
  return request({
    url: '/business/course-attendance/batchConfirm',
    method: 'post',
    data: attendanceIds
  })
}

// 导出托管考勤
export function exportCourseAttendance(params) {
  return request({
    url: '/business/course-attendance/export',
    method: 'get',
    params
  })
}

// 获取课程列表
export function listAllCourse(params) {
  return request({
    url: '/business/course/listAll',
    method: 'get',
    params
  })
}